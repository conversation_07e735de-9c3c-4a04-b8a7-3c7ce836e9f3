import React from 'react';
import <PERSON>konisLogo from '../Asserts/Makonis-Logo.png';

const StunningLoadingScreen = React.forwardRef(({ loadingProgress }, ref) => {

  const styles = `
    .stunning-loader-container {
      background: linear-gradient(135deg, #001f3f 0%, #00152b 50%, #000c1a 100%);
      z-index: 99999;
      overflow: hidden;
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .stunning-grid-bg {
      background-image:
        linear-gradient(rgba(0, 120, 200, 0.07) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 120, 200, 0.07) 1px, transparent 1px);
      background-size: 70px 70px;
      animation: stunningGridMove 40s linear infinite;
      opacity: 0.5;
      position: absolute;
      width: 100%;
      height: 100%;
    }
    @keyframes stunningGridMove {
      0% { background-position: 0 0; }
      100% { background-position: 140px 140px; }
    }

    ${[...Array(7)].map((_, i) => `
      .stunning-orb-${i} {
        width: ${100 + i * 30}px;
        height: ${100 + i * 30}px;
        background: radial-gradient(circle, rgba(0, 160, 233, ${0.05 - i * 0.005}) 0%, transparent 65%);
        filter: blur(${20 + i * 5}px);
        animation: stunningFloatOrb${i} ${12 + i * 3}s ease-in-out infinite;
        animation-delay: ${i * 0.7}s;
        pointer-events: none; /* Orbs should not interfere with interaction */
        position: absolute;
        border-radius: 50%;
      }
      @keyframes stunningFloatOrb${i} {
        0%, 100% {
          transform: translate(${Math.random() * 20 - 10}px, ${Math.random() * 20 - 10}px) scale(1);
          opacity: ${0.04 - i * 0.005};
        }
        50% {
          transform: translate(${Math.random() * 60 - 30}px, ${Math.random() * 60 - 30}px) scale(${1 + i * 0.03});
          opacity: ${0.06 - i * 0.005};
        }
      }
    `).join('')}

    .stunning-logo-perspective {
      perspective: 1500px;
    }

    .stunning-logo-3d-container {
      width: 250px;
      height: 250px;
      position: relative;
      margin: 0 auto;
      transform-style: preserve-3d;
    }

    /* New rotating globe behind the logo */
    .stunning-globe {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 300px;
      height: 300px;
      margin-top: -150px;
      margin-left: -150px;
      border-radius: 50%;
      background: radial-gradient(circle at center, #007acc 0%, #004a80 70%);
      box-shadow:
        inset 0 0 30px #00aaff,
        0 0 40px #00aaff;
      animation: globeRotate 20s linear infinite;
      z-index: 0;
    }
    @keyframes globeRotate {
      0% { transform: rotateY(0deg) rotateX(0deg); }
      100% { transform: rotateY(360deg) rotateX(360deg); }
    }

    .stunning-logo-face {
      background: radial-gradient(circle at 50% 40%, rgba(200, 235, 255, 0.9) 0%, rgba(150, 210, 240, 0.7) 100%);
      border: 3px solid rgba(0, 180, 255, 0.7);
      box-shadow:
        0 0 70px rgba(0, 180, 255, 0.7),
        inset 0 0 30px rgba(0, 150, 220, 0.4),
        0 0 10px rgba(255, 255, 255, 0.3);
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .stunning-logo-front { transform: translateZ(40px); }
    .stunning-logo-back { transform: translateZ(-40px) rotateY(180deg); }

    .stunning-logo-image {
      width: 120px;
      animation: stunningLogoGlow 2.2s ease-in-out infinite alternate;
    }
    @keyframes stunningLogoGlow {
      from {
        filter: drop-shadow(0 0 15px rgba(0, 200, 255, 0.8)) brightness(1.15) saturate(1.1);
        transform: scale(1);
      }
      to {
        filter: drop-shadow(0 0 30px rgba(0, 220, 255, 1)) brightness(1.3) saturate(1.2);
        transform: scale(1.03);
      }
    }

    ${[...Array(4)].map((_, i) => `
      .stunning-logo-ring-${i} {
        width: ${180 + i * 30}px;
        height: ${180 + i * 30}px;
        border: 2.5px solid rgba(0, 180, 255, ${0.4 - i * 0.08});
        box-shadow: 0 0 ${10 + i * 5}px rgba(0, 180, 255, ${0.3 - i * 0.05});
        animation: stunningRingRotate${i} ${7 + i * 2}s linear infinite;
        animation-delay: ${i * 0.5}s;
        position: absolute;
        top: 50%;
        left: 50%;
        border-radius: 50%;
        transform-style: preserve-3d;
        transform-origin: center center;
        margin: 0;
      }
      @keyframes stunningRingRotate${i} {
        0% { transform: translate(-50%, -50%) rotateX(${20 + i * 20}deg) translateZ(${i * 12}px) rotateY(0deg); }
        100% { transform: translate(-50%, -50%) rotateX(${20 + i * 20}deg) translateZ(${i * 12}px) rotateY(${i % 2 === 0 ? '360deg' : '-360deg'}); }
      }
    `).join('')}

    ${[...Array(12)].map((_, i) => `
      .stunning-logo-particle-${i} {
        width: ${3 + Math.floor(i/4)}px;
        height: ${3 + Math.floor(i/4)}px;
        background: rgba(0, 200, 255, ${0.6 + Math.random() * 0.4});
        box-shadow: 0 0 10px rgba(0, 220, 255, 0.8), 0 0 5px rgba(255,255,255,0.5);
        animation: stunningParticleOrbit${i} ${4 + Math.random() * 2}s linear infinite;
        animation-delay: ${i * 0.1}s;
        position: absolute;
        top: 50%;
        left: 50%;
        border-radius: 50%;
        transform-style: preserve-3d;
        transform-origin: center center;
        margin: 0;
      }
      @keyframes stunningParticleOrbit${i} {
        0% { transform: translate(-50%, -50%) rotate(${i * (360/12)}deg) translateX(${95 + (i%3)*10}px) translateZ(${Math.sin(0 * Math.PI / 180 + i*30) * 20}px) scale(0.8); opacity: 0.5; }
        50% { transform: translate(-50%, -50%) rotate(${i * (360/12) + 180}deg) translateX(${100 + (i%3)*10}px) translateZ(${Math.sin(180 * Math.PI / 180 + i*30) * 20}px) scale(1.1); opacity: 1;}
        100% { transform: translate(-50%, -50%) rotate(${i * (360/12) + 360}deg) translateX(${95 + (i%3)*10}px) translateZ(${Math.sin(360 * Math.PI / 180 + i*30) * 20}px) scale(0.8); opacity: 0.5;}
      }
    `).join('')}

    .stunning-loading-title {
      font-weight: 700;
      letter-spacing: 3px;
      font-size: 1.8rem;
      color: #e0f5ff;
      text-shadow: 0 0 10px rgba(0, 180, 255, 0.6), 0 0 20px rgba(0, 180, 255, 0.4);
      animation: stunningTextPulse 2.8s ease-in-out infinite;
    }
    @keyframes stunningTextPulse {
      0%, 100% { opacity: 0.8; transform: scale(1); }
      50% { opacity: 1; transform: scale(1.03); text-shadow: 0 0 15px rgba(0, 200, 255, 0.8), 0 0 30px rgba(0, 200, 255, 0.6); }
    }

    .stunning-motto { /* New style for the tagline */
      font-family: 'Source Code Pro', monospace; /* Techy, readable font */
      font-size: clamp(0.8rem, 2.5vw, 1.1rem); /* Responsive font size */
      color: rgba(180, 220, 255, 0.7);
      letter-spacing: 1.5px;
      text-transform: uppercase;
      text-shadow: 0 0 8px rgba(0, 180, 255, 0.3);
      margin-top: 0.5rem;
      margin-bottom: 2rem; /* More space before status */
      animation: stunningFadeIn 2s ease-out;
    }
    @keyframes stunningFadeIn {
        0% { opacity: 0; transform: translateY(10px); }
        100% { opacity: 1; transform: translateY(0); }
    }

    .stunning-loading-status {
      color: rgba(180, 220, 255, 0.8);
      font-size: 1rem;
      letter-spacing: 1.5px;
      animation: stunningFadeInOut 3s ease-in-out infinite;
      min-height: 1.5em;
    }
    @keyframes stunningFadeInOut {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }




    @keyframes stunningLogoGlowPulse {
      0%, 100% { filter: drop-shadow(0 0 15px rgba(0, 180, 255, 0.7)); }
      50% { filter: drop-shadow(0 0 30px rgba(0, 180, 255, 0.9)); }
    }

    .stunning-logo-face img {
      width: 100%; /* Ensure image scales within its container */
      height: auto;
      animation: stunningLogoGlowPulse 3s ease-in-out infinite;
    }

    /* Advanced Morphing Shapes */
    .morphing-shape {
      position: absolute;
      width: 120px;
      height: 120px;
      background: linear-gradient(45deg, rgba(0, 160, 233, 0.1), rgba(0, 160, 233, 0.3));
      border-radius: 50%;
      animation: morphShape 8s ease-in-out infinite;
      filter: blur(1px);
    }
    @keyframes morphShape {
      0%, 100% {
        border-radius: 50%;
        transform: rotate(0deg) scale(1);
        background: linear-gradient(45deg, rgba(0, 160, 233, 0.1), rgba(0, 160, 233, 0.3));
      }
      25% {
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        transform: rotate(90deg) scale(1.1);
        background: linear-gradient(45deg, rgba(0, 200, 255, 0.15), rgba(0, 120, 200, 0.25));
      }
      50% {
        border-radius: 20% 80% 20% 80% / 80% 20% 80% 20%;
        transform: rotate(180deg) scale(0.9);
        background: linear-gradient(45deg, rgba(0, 180, 255, 0.2), rgba(0, 140, 220, 0.3));
      }
      75% {
        border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
        transform: rotate(270deg) scale(1.05);
        background: linear-gradient(45deg, rgba(0, 220, 255, 0.12), rgba(0, 100, 180, 0.28));
      }
    }

    /* Holographic Effect */
    .hologram-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(0, 255, 255, 0.03) 50%,
        transparent 70%
      );
      background-size: 20px 20px;
      animation: hologramScan 3s linear infinite;
      pointer-events: none;
    }
    @keyframes hologramScan {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    /* Enhanced Glitch Effect */
    .glitch-text {
      position: relative;
      animation: glitchText 4s infinite;
    }
    .glitch-text::before,
    .glitch-text::after {
      content: attr(data-text);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .glitch-text::before {
      animation: glitchBefore 3s infinite;
      color: #ff0040;
      z-index: -1;
    }
    .glitch-text::after {
      animation: glitchAfter 3s infinite;
      color: #00ffff;
      z-index: -2;
    }
    @keyframes glitchText {
      0%, 90%, 100% { transform: translate(0); }
      10% { transform: translate(-2px, 2px); }
      20% { transform: translate(2px, -2px); }
      30% { transform: translate(-2px, -2px); }
      40% { transform: translate(2px, 2px); }
      50% { transform: translate(-2px, 2px); }
      60% { transform: translate(2px, -2px); }
      70% { transform: translate(-2px, -2px); }
      80% { transform: translate(2px, 2px); }
    }
    @keyframes glitchBefore {
      0%, 90%, 100% { transform: translate(0); }
      10% { transform: translate(2px, -2px); }
      20% { transform: translate(-2px, 2px); }
      30% { transform: translate(2px, 2px); }
      40% { transform: translate(-2px, -2px); }
      50% { transform: translate(2px, -2px); }
      60% { transform: translate(-2px, 2px); }
      70% { transform: translate(2px, 2px); }
      80% { transform: translate(-2px, -2px); }
    }
    @keyframes glitchAfter {
      0%, 90%, 100% { transform: translate(0); }
      10% { transform: translate(-2px, 2px); }
      20% { transform: translate(2px, -2px); }
      30% { transform: translate(-2px, -2px); }
      40% { transform: translate(2px, 2px); }
      50% { transform: translate(-2px, 2px); }
      60% { transform: translate(2px, -2px); }
      70% { transform: translate(-2px, -2px); }
      80% { transform: translate(2px, 2px); }
    }

    /* Floating Geometric Elements */
    .floating-geometry {
      position: absolute;
      opacity: 0.1;
      animation: floatGeometry 15s ease-in-out infinite;
    }
    @keyframes floatGeometry {
      0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.1;
      }
      50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 0.3;
      }
    }
  `;

  return (
    <>
      <style>{styles}</style>
      <div
        ref={ref}
        className="stunning-loader-container position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
      >
        {/* Animated Background Grid */}
        <div className="stunning-grid-bg position-absolute w-100 h-100" />

        {/* Floating Orbs (Atmosphere) */}
        {[...Array(7)].map((_, i) => (
          <div
            key={`stunning-orb-${i}`}
            className={`stunning-orb-${i} position-absolute rounded-circle`}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
          />
        ))}


        <div className="text-center position-relative" style={{ zIndex: 1 }}>
          {/* Rotating Globe */}
          <div className="stunning-globe" />

          {/* Static 3D Logo */}
          <div className="stunning-logo-perspective mb-5">
            <div className="stunning-logo-3d-container">
              {/* Front Logo Face */}
              <div className="stunning-logo-face stunning-logo-front">
                <img src={MakonisLogo} alt="Makonis Logo" className="stunning-logo-image" />
              </div>
              {/* Back Logo Face */}
              <div className="stunning-logo-face stunning-logo-back">
                <img src={MakonisLogo} alt="Makonis Logo Back" className="stunning-logo-image" style={{ transform: 'scaleX(-1)' }} />
              </div>
              {/* Side Rings */}
              {[...Array(4)].map((_, i) => (
                <div
                  key={`stunning-ring-${i}`}
                  className={`stunning-logo-ring-${i}`}
                />
              ))}
              {/* Particles around Logo */}
              {[...Array(12)].map((_, i) => (
                <div
                  key={`stunning-particle-${i}`}
                  className={`stunning-logo-particle-${i}`}
                />
              ))}
            </div>
          </div>

          {/* Loading Title */}
          <h3 className="stunning-loading-title text-white mb-3">
            MAKONIS SOFTWARE
          </h3>

          {/* Loading Status */}
          <div className="stunning-loading-status mb-4">
            {loadingProgress < 40 && "Initializing Core Systems..."}
            {loadingProgress >= 40 && loadingProgress < 80 && "Loading Interface Modules..."}
            {loadingProgress >= 80 && "Almost Ready to Launch..."}
          </div>
        </div>
      </div>
    </>
  );
});

export default StunningLoadingScreen;