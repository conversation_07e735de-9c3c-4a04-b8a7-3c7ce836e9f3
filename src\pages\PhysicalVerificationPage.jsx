import React, { useEffect, useState, useRef } from 'react';
import { Con<PERSON>er, <PERSON>, Col, Button, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import 'animate.css';

gsap.registerPlugin(ScrollTrigger);

const physicalVerificationData = {
  hero: {
    title: "Precision Physical Verification Services",
    subtitle: "Ensuring your semiconductor designs meet all manufacturing and quality standards with unmatched precision and reliability.",
  },
  intro: {
    title: "Comprehensive Physical Verification Solutions",
    description: "Our physical verification services include Design Rule Checking (DRC), Layout Versus Schematic (LVS), Electrical Rule Checking (ERC), and antenna checks to guarantee your chip's manufacturability and reliability. We ensure your design is robust and production-ready.",
    image: "https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" // More tech-focused image
  },
  services: [
    {
      id: "drc",
      title: "Design Rule Checking (DRC)",
      description: "Verifying that the layout strictly adheres to the foundry's design rules to prevent manufacturing defects and ensure yield.",
      features: [
        "Foundry rule deck development",
        "Automated DRC runs & sign-off",
        "Efficient error analysis and reporting",
        "Hierarchical & flat DRC methodologies"
      ],
      icon: "fas fa-ruler-combined",
      image: "https://images.unsplash.com/photo-1591696205602-2f950c417cb9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80", // Tech image
      color: "#00a0e9"
    },
    {
      id: "lvs",
      title: "Layout Versus Schematic (LVS)",
      description: "Ensuring the physical layout precisely matches the original schematic netlist to avoid functional errors and ensure design integrity.",
      features: [
        "Accurate netlist extraction",
        "Comprehensive layout-to-schematic comparison",
        "Efficient mismatch resolution and debugging",
        "Handling complex hierarchical designs"
      ],
      icon: "fas fa-project-diagram",
      image: "https://images.unsplash.com/photo-1518773553398-650c184e0bb3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      color: "#0056b3"
    },
    {
      id: "erc",
      title: "Electrical Rule Checking (ERC)",
      description: "Performing critical checks for electrical violations such as antenna effects, floating nodes, and power issues that can impact reliability.",
      features: [
        "Advanced antenna checks & fixing",
        "Robust power network verification",
        "Signal integrity analysis & optimization",
        "Electrostatic discharge (ESD) verification"
      ],
      icon: "fas fa-bolt",
      image: "https://images.unsplash.com/photo-1605647540924-852290ab8b78?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80", // Tech image
      color: "#00a0e9"
    },
    {
      id: "dfm",
      title: "Design for Manufacturability (DFM)",
      description: "Analyzing and optimizing the layout to improve manufacturing yield and reliability by identifying process hot spots.",
      features: [
        "Critical area analysis (CAA)",
        "Lithography Hotspot Detection",
        "Yield Enhancement Analysis",
        "Advanced process variation checks"
      ],
      icon: "fas fa-industry",
      image: "https://images.unsplash.com/photo-1581092497678-83149e32f523?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      color: "#0056b3"
    }
  ],
  cta: {
    title: "Ensure Your Chip's Quality and Reliability",
    text: "Partner with us for comprehensive physical verification. Let's guarantee your chip's manufacturability and achieve flawless production.",
    buttonText: "Contact Us Today",
    buttonLink: "/contact"
  }
};

const PhysicalVerificationPage = () => {
  // Loading state
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const loadingRef = useRef(null);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [isCtaButtonHovered, setIsCtaButtonHovered] = useState(false);
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaButtonRef = useRef(null); // Ref for hero CTA button
  const centralChipRef = useRef(null); // Ref for the new central chip

  // Loading animation effect
  useEffect(() => {
    // Simulate loading progress for 2 seconds
    const progressInterval = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          // Start exit animation
          gsap.to(loadingRef.current, {
            opacity: 0,
            scale: 0.8,
            duration: 0.8,
            ease: "power2.inOut",
            onComplete: () => {
              setIsLoading(false);
            }
          });
          return 100;
        }
        return prev + Math.random() * 8 + 4; // Faster increment for 2-second loading
      });
    }, 100); // Faster interval for 2-second loading

    return () => clearInterval(progressInterval);
  }, []);

  // Style constants
  const primaryColor = '#002956'; // Deep Blue
  const secondaryColor = '#0D1B2A'; // Even Darker Blue
  const accentColor = '#00a0e9'; // Bright Blue Accent
  const accentRgb = '0, 160, 233';
  const textColor = 'rgba(255, 255, 255, 0.9)';
  const headingColor = '#ffffff';

  const darkBackgroundGradient = `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 70%, #000f1f 100%)`;
  const cardBackground = 'rgba(255, 255, 255, 0.03)'; // Subtle background for cards
  const cardBorder = `1px solid rgba(${accentRgb}, 0.2)`;

  // Button styles
  const ctaButtonBaseStyle = {
    padding: '1.2rem 3rem',
    fontSize: '1.2rem',
    background: `linear-gradient(95deg, ${accentColor}, ${primaryColor})`,
    border: 'none',
    borderRadius: '50px',
    boxShadow: `0 8px 25px rgba(${accentRgb}, 0.35)`,
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    transform: 'translateY(0) scale(1)',
    color: '#fff',
    textDecoration: 'none',
    display: 'inline-flex', // Changed to inline-flex for icon alignment
    alignItems: 'center',
    fontWeight: 'bold',
  };

  const ctaButtonHoverStyle = {
    boxShadow: `0 15px 35px rgba(${accentRgb}, 0.45)`,
    transform: 'translateY(-3px) scale(1.03)',
    background: `linear-gradient(95deg, ${accentColor}, #004080)`, // Slightly lighter on hover
  };


  useEffect(() => {
    if (!isLoading) {
      const ctx = gsap.context(() => {
        // Hero entrance animation with delay for loading
        const tl = gsap.timeline({ delay: 0.3 });

        tl.from(centralChipRef.current, {
          opacity: 0,
          scale: 0.5,
          duration: 1.2,
          ease: "power3.out"
        })
        .from(titleRef.current, {
          y: 100,
          opacity: 0,
          duration: 1,
          ease: "power3.out"
        }, "-=0.8")
        .from(subtitleRef.current, {
          y: 50,
          opacity: 0,
          duration: 0.8,
          ease: "power2.out"
        }, "-=0.7")
        .from(ctaButtonRef.current, { // Animating the hero CTA button
          opacity: 0,
          y: 30,
          duration: 0.7,
          ease: "power2.out"
        }, "-=0.5");

      // GSAP ScrollTrigger for Intro Section
      gsap.from("#intro-section .animated-col", {
        x: (i) => i % 2 === 0 ? -80 : 80,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
        stagger: 0.25,
        scrollTrigger: {
          trigger: "#intro-section",
          start: "top 80%",
          toggleActions: "play none none none",
        }
      });
      gsap.from("#intro-section .intro-image-wrapper", {
         opacity:0,
         scale: 0.8,
         duration: 1,
         ease: "power3.out",
         scrollTrigger: {
          trigger: "#intro-section",
          start: "top 75%",
          toggleActions: "play none none none",
        }
      });


      // GSAP ScrollTrigger for Services Header
      gsap.from("#services-section .section-header-animate", {
        y: 70,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: "#services-section",
          start: "top 80%",
          toggleActions: "play none none none",
        }
      });

      // GSAP ScrollTrigger for Service Cards
      gsap.from(".service-card-gsap", {
        y: 60,
        opacity: 0,
        duration: 0.8,
        ease: "power2.out",
        stagger: 0.2,
        scrollTrigger: {
          trigger: "#services-section .row",
          start: "top 75%", // Start animation a bit earlier
          toggleActions: "play none none none",
        }
      });
      
      // Floating particles in Hero
      const particles = heroRef.current.querySelectorAll('.hero-particle');
      particles.forEach(particle => {
        gsap.set(particle, {
            x: gsap.utils.random(-window.innerWidth / 2, window.innerWidth / 2),
            y: gsap.utils.random(-window.innerHeight / 2, window.innerHeight / 2),
            scale: gsap.utils.random(0.3, 1),
            opacity: 0,
        });
        gsap.to(particle, {
            x: `+=${gsap.utils.random(-100, 100)}`,
            y: `+=${gsap.utils.random(-100, 100)}`,
            opacity: gsap.utils.random(0.1, 0.5),
            duration: gsap.utils.random(5, 15),
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut",
            delay: gsap.utils.random(0, 5)
        });
      });


      }, heroRef); // Scope GSAP animations to heroRef

      return () => ctx.revert();
    }
  }, [isLoading]);


  return (
    <>

      <div className="physical-verification-page" style={{ overflowX: 'hidden', background: darkBackgroundGradient }}>
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="position-relative overflow-hidden"
        style={{
          minHeight: '100vh',
          background: darkBackgroundGradient,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '100px 0',
        }}
      >
        {/* Enhanced Background Elements */}
        <div className="position-absolute w-100 h-100 top-0 start-0" style={{ zIndex: 0 }}>
          {/* Particles */}
          {[...Array(50)].map((_, i) => (
            <div
              key={`particle-${i}`}
              className="hero-particle position-absolute"
              style={{
                width: `${Math.random() * 3 + 1}px`,
                height: `${Math.random() * 3 + 1}px`,
                background: `rgba(${accentRgb}, ${Math.random() * 0.5 + 0.2})`,
                borderRadius: '50%',
                boxShadow: `0 0 ${Math.random() * 6 + 2}px rgba(${accentRgb}, 0.5)`,
                left: `${Math.random() * 100}%`, // Initial position, GSAP will take over
                top: `${Math.random() * 100}%`,   // Initial position
              }}
            />
          ))}

          {/* Floating Tech Elements */}
          {[...Array(8)].map((_, i) => (
            <div
              key={`tech-element-${i}`}
              className="floating-tech-element"
              style={{
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                width: `${30 + Math.random() * 40}px`,
                height: `${30 + Math.random() * 40}px`,
                background: `rgba(${accentRgb}, ${0.05 + Math.random() * 0.1})`,
                borderRadius: i % 3 === 0 ? '50%' : i % 3 === 1 ? '0%' : '25%',
                animationDelay: `${Math.random() * 5}s`,
                border: `1px solid rgba(${accentRgb}, 0.2)`,
              }}
            />
          ))}

          {/* Morphing Background Elements */}
          {[...Array(3)].map((_, i) => (
            <div
              key={`morph-bg-${i}`}
              className="morphing-bg-element"
              style={{
                top: `${20 + i * 25}%`,
                right: `${10 + i * 20}%`,
                width: `${80 + i * 30}px`,
                height: `${80 + i * 30}px`,
                animationDelay: `${i * 3}s`,
              }}
            />
          ))}

           {/* Subtle Grid Overlay */}
          <div className="position-absolute w-100 h-100 top-0 start-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(${accentRgb}, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(${accentRgb}, 0.03) 1px, transparent 1px)
              `,
              backgroundSize: '30px 30px',
              opacity: 0.5,
            }}
          ></div>
        </div>

        {/* Central Chip Visual Element */}
        <div
          ref={centralChipRef}
          className="position-absolute glow-pulse"
          style={{
            width: '200px',
            height: '200px',
            zIndex: 1,
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -calc(50% + 50px))', // Position above text
            opacity: 0.4, // Slightly more visible with glow effect
          }}
        >
          <div className="central-chip-core" style={{
             width: '100%', height: '100%',
             borderRadius: '25px',
             background: `radial-gradient(circle, rgba(${accentRgb}, 0.4) 0%, rgba(${accentRgb}, 0.08) 60%)`,
             boxShadow: `0 0 40px rgba(${accentRgb},0.3), inset 0 0 20px rgba(${accentRgb},0.15)`,
             animation: 'pulseCore 3s infinite ease-in-out',
             position: 'relative',
             border: `2px solid rgba(${accentRgb}, 0.3)`,
          }}>
            <div style={{ // Inner square
                position: 'absolute', top: '25%', left: '25%', width: '50%', height: '50%',
                background: `rgba(${accentRgb}, 0.3)`,
                borderRadius: '10px',
                boxShadow: `0 0 20px rgba(${accentRgb},0.4)`,
                border: `1px solid rgba(${accentRgb}, 0.5)`,
            }}></div>

            {/* Additional inner elements for more complexity */}
            <div style={{
                position: 'absolute', top: '10%', left: '10%', width: '15%', height: '15%',
                background: `rgba(${accentRgb}, 0.2)`,
                borderRadius: '50%',
                boxShadow: `0 0 10px rgba(${accentRgb},0.3)`,
            }}></div>
            <div style={{
                position: 'absolute', top: '10%', right: '10%', width: '15%', height: '15%',
                background: `rgba(${accentRgb}, 0.2)`,
                borderRadius: '50%',
                boxShadow: `0 0 10px rgba(${accentRgb},0.3)`,
            }}></div>
            <div style={{
                position: 'absolute', bottom: '10%', left: '10%', width: '15%', height: '15%',
                background: `rgba(${accentRgb}, 0.2)`,
                borderRadius: '50%',
                boxShadow: `0 0 10px rgba(${accentRgb},0.3)`,
            }}></div>
            <div style={{
                position: 'absolute', bottom: '10%', right: '10%', width: '15%', height: '15%',
                background: `rgba(${accentRgb}, 0.2)`,
                borderRadius: '50%',
                boxShadow: `0 0 10px rgba(${accentRgb},0.3)`,
            }}></div>
          </div>
           {/* Enhanced circuit lines */}
          {[0, 45, 90, 135, 180, 225, 270, 315].map(angle => (
            <div key={angle} style={{
              position: 'absolute', top: '50%', left: '50%',
              width: '150%', height: '2px',
              background: `linear-gradient(90deg, transparent, rgba(${accentRgb}, 0.3), transparent)`,
              transformOrigin: '0% 50%',
              transform: `translate(0, -1px) rotate(${angle}deg) translateX(50%)`,
              opacity: 0.8,
              boxShadow: `0 0 4px rgba(${accentRgb}, 0.4)`,
            }}></div>
          ))}
        </div>


        <Container className="position-relative text-center" style={{ zIndex: 2 }}>
          <Row className="justify-content-center">
            <Col lg={10} md={12}>
              <div ref={titleRef} className="mb-4">
                <h1
                  className="display-3 fw-bold mb-4"
                  style={{
                    color: headingColor,
                    textShadow: `0 0 25px rgba(${accentRgb}, 0.4), 0 0 10px rgba(${accentRgb}, 0.2)`
                  }}
                >
                  {physicalVerificationData.hero.title}
                </h1>
              </div>
              <div ref={subtitleRef}>
                <p className="lead mb-5 mx-auto" style={{ color: textColor, maxWidth: '750px', fontSize: '1.25rem' }}>
                  {physicalVerificationData.hero.subtitle}
                </p>
              </div>
              <div ref={ctaButtonRef}>
                <Link
                  to="/contact"
                  style={isCtaButtonHovered ? {...ctaButtonBaseStyle, ...ctaButtonHoverStyle} : ctaButtonBaseStyle}
                  onMouseEnter={() => setIsCtaButtonHovered(true)}
                  onMouseLeave={() => setIsCtaButtonHovered(false)}
                >
                  Explore Services <i className="fas fa-arrow-right ms-2"></i>
                </Link>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Intro Section */}
      <section className="py-5 py-md-6" id="intro-section" style={{ background: secondaryColor, borderTop: `2px solid rgba(${accentRgb},0.1)` }}>
        <Container>
          <Row className="align-items-center g-5">
            <Col lg={6} className="animated-col">
              <div className="mb-3">
                <span style={{
                    display: 'inline-block', padding: '10px 20px',
                    background: `rgba(${accentRgb}, 0.1)`, borderRadius: '30px',
                    color: accentColor, fontSize: '0.9rem', fontWeight: 600,
                    border: `1px solid rgba(${accentRgb}, 0.2)`
                }}>
                  <i className="fas fa-microchip me-2"></i> Verification Excellence
                </span>
              </div>
              <h2 className="display-4 fw-bold mb-4" style={{ color: headingColor }}>
                {physicalVerificationData.intro.title}
              </h2>
              <p className="lead mb-4" style={{ fontSize: '1.15rem', lineHeight: '1.8', color: textColor }}>
                {physicalVerificationData.intro.description}
              </p>
            </Col>
            <Col lg={6} className="animated-col">
              <div className="intro-image-wrapper position-relative rounded-4 overflow-hidden shadow-lg" 
                   style={{ 
                     border: `3px solid rgba(${accentRgb}, 0.2)`,
                     boxShadow: `0 10px 40px rgba(${accentRgb}, 0.2)`
                   }}>
                <img
                  src={physicalVerificationData.intro.image}
                  alt="Physical Verification Process"
                  className="w-100 h-100"
                  style={{ objectFit: 'cover', display:'block', transition: 'transform 0.5s ease' }}
                  onMouseEnter={(e) => { e.target.style.transform = 'scale(1.03)' }}
                  onMouseLeave={(e) => { e.target.style.transform = 'scale(1)' }}
                />
                 <div style={{
                    position: 'absolute', top: 0, left: 0, width: '100%', height: '100%',
                    background: `linear-gradient(45deg, rgba(${primaryColor},0.2), transparent 70%)`,
                    pointerEvents: 'none'
                 }}></div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Services Section */}
      <section className="py-5 py-md-6" id="services-section" style={{ background: darkBackgroundGradient }}>
        <Container>
          <div className="text-center mb-5 section-header-animate">
             <span style={{
                  display: 'inline-block', padding: '10px 20px',
                  background: `rgba(${accentRgb}, 0.1)`, borderRadius: '30px',
                  color: accentColor, fontSize: '0.9rem', fontWeight: 600,
                  border: `1px solid rgba(${accentRgb}, 0.2)`, marginBottom: '1rem'
              }}>
                <i className="fas fa-cogs me-2"></i> Our Expertise
              </span>
            <h2 className="display-4 fw-bold mb-3" style={{ color: headingColor }}>Ensuring Silicon Perfection</h2>
            <p className="lead mx-auto" style={{ maxWidth: '750px', color: textColor }}>
              We offer a full range of physical verification services to ensure your semiconductor designs are manufacturable, reliable, and error-free.
            </p>
          </div>

          <Row className="g-4 justify-content-center">
            {physicalVerificationData.services.map((service) => (
              <Col key={service.id} lg={4} md={6} sm={12} className="service-card-gsap d-flex">
                <Card
                  className="h-100 border-0 rounded-4 service-card-enhanced"
                  style={{
                    background: cardBackground,
                    border: cardBorder,
                    transition: 'transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease',
                    overflow: 'hidden', // Ensure content respects border-radius
                  }}
                >
                  <div style={{
                      height: '220px', overflow: 'hidden', position: 'relative',
                      backgroundImage: `url(${service.image})`, backgroundSize: 'cover', backgroundPosition: 'center'
                  }}>
                    <div className="service-card-image-overlay" style={{
                        position: 'absolute', top:0, left:0, width:'100%', height:'100%',
                        background: `linear-gradient(to top, ${service.color}99, transparent 70%)`,
                        opacity: 0.8, transition: 'opacity 0.3s ease'
                    }}></div>
                    <div className="service-card-icon-container" style={{
                        position: 'absolute', top: '20px', right: '20px',
                        background: 'rgba(0,0,0,0.4)', padding: '15px', borderRadius: '50%',
                        color: '#fff', fontSize: '1.5rem',
                        boxShadow: '0 4px 10px rgba(0,0,0,0.3)',
                        transition: 'transform 0.3s ease, background 0.3s ease'
                    }}>
                        <i className={`${service.icon}`}></i>
                    </div>
                  </div>

                  <Card.Body className="d-flex flex-column p-4">
                    <Card.Title className="fw-bold mb-3" style={{ color: accentColor, fontSize: '1.7rem' }}>
                      {service.title}
                    </Card.Title>
                    <Card.Text className="flex-grow-1" style={{ fontSize: '1rem', color: 'rgba(255, 255, 255, 0.8)', lineHeight: 1.7 }}>
                      {service.description}
                    </Card.Text>
                    <ul className="list-unstyled mt-4 mb-4">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="mb-2 d-flex align-items-start" style={{ fontSize: '0.95rem', color: 'rgba(255, 255, 255, 0.75)' }}>
                          <i className="fas fa-check-circle me-2 mt-1" style={{ color: service.color, fontSize: '0.9em' }}></i> {feature}
                        </li>
                      ))}
                    </ul>
                    <Button
                        as={Link}
                        to={`/services/physical-verification/${service.id}`} // Example link
                        variant="outline-light"
                        className="mt-auto align-self-start fw-bold rounded-pill px-4 py-2"
                        style={{
                            borderColor: service.color,
                            color: service.color,
                            transition: 'background-color 0.3s ease, color 0.3s ease',
                        }}
                        onMouseEnter={e => { e.currentTarget.style.backgroundColor = service.color; e.currentTarget.style.color = '#fff';}}
                        onMouseLeave={e => { e.currentTarget.style.backgroundColor = 'transparent'; e.currentTarget.style.color = service.color;}}
                    >
                        Learn More <i className="fas fa-long-arrow-alt-right ms-2"></i>
                    </Button>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>
      
      {/* CTA Section - Reusing existing logic but ensuring style consistency */}
      <section id="cta-section" className="py-5 py-md-6 text-center" style={{ background: secondaryColor, borderTop: `2px solid rgba(${accentRgb},0.1)`}}>
        <Container>
            <h2 className="display-4 fw-bolder mb-4" style={{color: headingColor}}>{physicalVerificationData.cta.title}</h2>
            <p className="lead mb-5 mx-auto" style={{maxWidth: '700px', color: textColor}}>
                {physicalVerificationData.cta.text}
            </p>
            <Link
                to={physicalVerificationData.cta.buttonLink}
                style={isCtaButtonHovered ? {...ctaButtonBaseStyle, ...ctaButtonHoverStyle} : ctaButtonBaseStyle}
                onMouseEnter={() => setIsCtaButtonHovered(true)} // Assuming you might reuse isCtaButtonHovered or create a new state variable
                onMouseLeave={() => setIsCtaButtonHovered(false)} // if this button is different
            >
                {physicalVerificationData.cta.buttonText} <i className="fas fa-paper-plane ms-2"></i>
            </Link>
        </Container>
      </section>


      {/* CSS Keyframes (some might be unused from original, kept for potential use) */}
      <style>{`
        .physical-verification-page {
          scroll-behavior: smooth;
        }
        .service-card-enhanced:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(${accentRgb}, 0.25);
          background: rgba(255, 255, 255, 0.05);
        }
        .service-card-enhanced:hover .service-card-image-overlay {
            opacity: 0.6;
        }
        .service-card-enhanced:hover .service-card-icon-container {
            transform: scale(1.1);
            background: rgba(0,0,0,0.6);
        }
        
        @keyframes pulseCore {
            0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(${accentRgb}, 0.4); }
            70% { transform: scale(1); box-shadow: 0 0 0 20px rgba(${accentRgb}, 0); }
            100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(${accentRgb}, 0); }
        }

        /* Enhanced Floating Elements */
        .floating-tech-element {
          position: absolute;
          opacity: 0.1;
          animation: floatTech 12s ease-in-out infinite;
          pointer-events: none;
        }
        @keyframes floatTech {
          0%, 100% {
            transform: translateY(0) rotate(0deg) scale(1);
            opacity: 0.1;
          }
          50% {
            transform: translateY(-40px) rotate(180deg) scale(1.1);
            opacity: 0.3;
          }
        }

        /* Advanced Glow Effects */
        .glow-pulse {
          animation: glowPulse 3s ease-in-out infinite;
        }
        @keyframes glowPulse {
          0%, 100% {
            filter: drop-shadow(0 0 10px rgba(${accentRgb}, 0.3));
          }
          50% {
            filter: drop-shadow(0 0 25px rgba(${accentRgb}, 0.6));
          }
        }

        /* Morphing Background Elements */
        .morphing-bg-element {
          position: absolute;
          background: linear-gradient(45deg, rgba(${accentRgb}, 0.05), rgba(${accentRgb}, 0.15));
          border-radius: 50%;
          animation: morphBg 15s ease-in-out infinite;
          filter: blur(2px);
        }
        @keyframes morphBg {
          0%, 100% {
            border-radius: 50%;
            transform: rotate(0deg) scale(1);
          }
          25% {
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            transform: rotate(90deg) scale(1.2);
          }
          50% {
            border-radius: 20% 80% 20% 80% / 80% 20% 80% 20%;
            transform: rotate(180deg) scale(0.8);
          }
          75% {
            border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
            transform: rotate(270deg) scale(1.1);
          }
        }

        /* Enhanced Service Card Animations */
        .service-card-enhanced {
          position: relative;
          overflow: hidden;
        }
        .service-card-enhanced::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: linear-gradient(
            45deg,
            transparent,
            rgba(${accentRgb}, 0.1),
            transparent
          );
          transform: rotate(45deg);
          transition: all 0.6s ease;
          opacity: 0;
        }
        .service-card-enhanced:hover::before {
          animation: shimmer 1.5s ease-in-out;
          opacity: 1;
        }
        @keyframes shimmer {
          0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
          100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        // Enhanced visual effects and animations
      `}</style>
      </div>
    </>
  );
};

export default PhysicalVerificationPage;