import React, { useState, useEffect } from "react";
import { FaLinkedin, FaEnvelope } from "react-icons/fa";
 
const TeamPage = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      description:
        "Serial Entrepreneur, over 18 years of experience managing technology business.",
      linkedin: "linkedin.com/in/krishna-samanth-bb0a46b5/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "<PERSON><PERSON> <PERSON>",
      role: "COO & Co-Founder",
      description:
        "Over 30 years of experience in Global Software Delivery, Vision, Strategy and Operational Excellence.",
      linkedin: "linkedin.com/in/durga-prasad-a-632ab61a1/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "<PERSON><PERSON>",
      role: "CHRO & Co-Founder - Dubai",
      description:
        "30 years in Global HR functions for IT (Products / Projects) and Retail sectors.",
      linkedin: "linkedin.com/in/sc007/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "V<PERSON>hree",
      role: "CTO & Managing Partner - Australia",
      description:
        "20 years in IT Development and Enterprise Delivery across Finance, Banking, Telecom, and Healthcare.",
      linkedin: "linkedin.com/in/vamshree/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Sreeni Raju",
      role: "Head – ERP Software Solutions & Delivery - USA",
      description:
        "22 Years of experience in core HR, global ERP implementations and consulting in some of the global fortune 500 companies.",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Nishant Seth",
      role: "Practice Head- Data, AI/ML and Analytics",
      description:
        "Data Analytics consultant with 25+ years in AI/ML, data lakes, and analytics across industries.",
      linkedin: "linkedin.com/in/nishantseth/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Kishore Seetharam",
      role: "Head - Talent Acquisition",
      description: "Over 17 Years of Experience in Technical Recruitment.",
      linkedin: "linkedin.com/in/seetharam-kishore-25a9881a/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Dr. Mehala N",
      role: "Advisor",
      description:
        "20+ years in academia, teaching, content development, and research in Data Science & Machine Intelligence.",
      linkedin: "linkedin.com/in/dr-mehala-n-********/",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Vinesh Singh",
      role: "Head - L&D",
      description:
        "Over 35 years in Finance, Accounting, strategic planning, Budgeting, Joint ventures and Vendor management",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
    {
      name: "Sai Krishna",
      role: "Head - Product & Innovation",
      description:
        "Over 13+ years of experience managing research and development in IoT",
      email: "<EMAIL>",
      image: "https://placehold.co/400x400",
    },
  ];
 
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
 
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
 
  // Responsive breakpoints - ADJUSTED FOR IPAD PRO 1024px width
  const isDesktop = windowWidth > 1024; // Desktop starts *above* 1024px
  const isTablet = windowWidth >= 600 && windowWidth <= 1024; // Tablet now includes 1024px
  const isMobile = windowWidth < 600;
 
  // The number of cards per row is used for logic but actual layout is handled by flex
  const cardsPerRow = isDesktop ? 4 : isTablet ? 3 : 2;
 
  // Create rows for centering logic (still useful even with flex layout)
  const rows = [];
  for (let i = 0; i < teamMembers.length; i += cardsPerRow) {
    rows.push(teamMembers.slice(i, i + cardsPerRow));
  }
 
  //  the wireframe dimensions
  const baseCardWidth = 244.17;
  const cardHeight = 434.66;
  const gap = 28; // Consistent gap between cards
 
  const Card = ({ member }) => (
    <div
      style={{
        background: "#fff",
        borderRadius: 16,
        padding: "1.8rem",
        boxShadow:
          "0 4px 12px rgba(0,0,0,0.25), inset 0 0 15px rgba(255,255,255,0.05)",
        display: "flex",
        flexDirection: "column",
        transition: "transform 0.3s ease, box-shadow 0.3s ease",
        cursor: "default",
        height: cardHeight, // Target wireframe height
        boxSizing: "border-box", // Include padding in element's total dimensions
        overflow: "hidden", // Hide any overflowing content
 
        // FLEXBOX PROPERTIES FOR ADAPTIVE CARD SIZING
        flexGrow: 1, // Allow cards to grow if there's extra space
        flexShrink: 1, // Allow cards to shrink if space is limited (crucial for mobile)
 
        // flexBasis determines the ideal size before growing/shrinking
        // Adjusted to use percentages that account for the gap, ensuring proper distribution
        flexBasis: isDesktop
          ? `calc(${100 / 4}% - ${gap * (4 - 1) / 4}px)`
          : isTablet
          ? `calc(${100 / 3}% - ${gap * (3 - 1) / 3}px)`
          : `calc(${100 / 2}% - ${gap * (2 - 1) / 2}px)`,
 
        // maxWidth ensures cards don't exceed the ideal size or cause overflow on mobile
        maxWidth: isMobile
          ? `calc(${100 / 2}% - ${gap * (2 - 1) / 2}px)` // On mobile, cap at 50% minus half gap to ensure 2 per row
          : baseCardWidth, // On tablet/desktop, cap at the wireframe width
 
        // minWidth prevents cards from becoming unreadably small on very narrow screens
        minWidth: isMobile ? '125px' : '200px',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = "scale(1.05)";
        e.currentTarget.style.boxShadow =
          "0 10px 30px rgba(0,0,0,0.5), inset 0 0 20px rgba(255,255,255,0.1)";
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = "scale(1)";
        e.currentTarget.style.boxShadow =
          "0 4px 12px rgba(0,0,0,0.25), inset 0 0 15px rgba(255,255,255,0.05)";
      }}
    >
      <img
        src={member.image}
        alt={member.name}
        style={{
          width: 130,
          height: 130,
          borderRadius: "50%",
          objectFit: "cover",
          margin: "0 auto 1.3rem",
          border: "3px solid #00a0e9",
          filter: "grayscale(5%) contrast(1.05)",
          transition: "filter 0.3s ease",
          flexShrink: 0, // Prevent image from shrinking
        }}
      />
      <h5
        style={{
          color: "gray",
          fontWeight: "700",
          fontSize: isMobile ? "0.85rem" : "1.25rem",
          letterSpacing: 0.8,
          marginBottom: 6,
          textAlign: "center",
          wordBreak: 'break-word', // Allow long words to wrap
        }}
      >
        {member.name}
      </h5>
      <h6
        style={{
          color: "#000",
          fontWeight: "600",
          fontSize: isMobile ? "0.75rem" : "1rem",
          marginBottom: 12,
          minHeight: "2.2em", // Consistent height for role to avoid layout shifts
          textAlign: "center",
          wordBreak: 'break-word', // Allow long words to wrap
        }}
      >
        {member.role}
      </h6>
      <p
        style={{
          color: "#000",
          fontSize: isMobile ? "0.65rem" : "0.95rem",
          lineHeight: 1.3, // Slightly tighter line height to save space
          fontWeight: "400",
          flexGrow: 1, // Allow description to take available vertical space
          marginBottom: 14,
          textAlign: "center",
          wordBreak: 'break-word', // Allow long words to wrap
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: isMobile ? 5 : 4, // Increased line clamp for mobile to allow more text visibility
          WebkitBoxOrient: 'vertical',
        }}
      >
        {member.description}
      </p>
      {(member.linkedin || member.email) && (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            gap: 14,
            marginTop: "auto", // Pushes icons to the bottom
          }}
        >
          {member.linkedin && (
            <a
              href={`https://${member.linkedin}`}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={`LinkedIn profile of ${member.name}`}
              style={iconStyle}
              onMouseEnter={iconHoverIn}
              onMouseLeave={iconHoverOut}
            >
              <FaLinkedin style={{ color: "gray" }} />
            </a>
          )}
          {member.email && (
            <a
              href={`mailto:${member.email}`}
              aria-label={`Send email to ${member.name}`}
              style={iconStyle}
              onMouseEnter={iconHoverIn}
              onMouseLeave={iconHoverOut}
            >
              <FaEnvelope style={{ color: "gray" }} />
            </a>
          )}
        </div>
      )}
    </div>
  );
 
  const iconStyle = {
    color: "#00a0e9",
    fontSize: "1.3rem",
    background: "rgba(168, 168, 168, 0.15)",
    padding: 9,
    borderRadius: 10,
    border: "1.3px solid rgba(80, 80, 80, 0.3)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: 36,
    height: 36,
    transition: "all 0.3s ease",
    cursor: "pointer",
  };
 
  function iconHoverIn(e) {
    e.currentTarget.style.background = "rgba(0, 160, 233, 0.4)";
    e.currentTarget.style.color = "#fff";
    e.currentTarget.style.boxShadow = "0 6px 18px rgba(0, 160, 233, 0.6)";
  }
 
  function iconHoverOut(e) {
    e.currentTarget.style.background = "rgba(0, 160, 233, 0.15)";
    e.currentTarget.style.color = "#00a0e9";
    e.currentTarget.style.boxShadow = "0 2px 8px rgba(0, 160, 233, 0.25)";
  }
 
  // Calculate last row centering style
  const lastRow = rows[rows.length - 1];
  let justifyContent = "start";
  if (
    (isDesktop && lastRow.length < 4) ||
    (isTablet && lastRow.length < 3) ||
    (isMobile && lastRow.length < 2)
  ) {
    justifyContent = "center";
  }
 
  return (
    <div
      style={{
        minHeight: "100vh",
        background: "linear-gradient(180deg, #001533 0%, #002a70 100%)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: "4rem 1.5rem 6rem", // Overall page padding
        fontFamily: "'Poppins', sans-serif",
      }}
    >
      <div
        style={{
          maxWidth: 900,
          textAlign: "center",
          marginBottom: "2.5rem",
          padding: "0 1rem", // Padding for the title section
        }}
      >
        <h1
          style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}
        >
          Our Leadership
        </h1>
        <p
          style={{
            color: "#cbd5e1",
            fontSize: "1.2rem",
            lineHeight: "1.9",
            margin: "0 auto",
            fontWeight: 500,
            opacity: 0.9,
          }}
        >
          Since its inception, Makonis has thrived under exceptional leadership — from
          our strong, engaged, and independent board members to our experienced and
          globally distributed senior management team.
        </p>
      </div>
 
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          justifyContent,
          gap: gap, // Use the defined gap variable
          width: '100%', // Container takes full width of its parent (minus parent's padding)
          boxSizing: 'border-box', // Crucial: padding inside width
          // Max width for the entire grid of cards, based on ideal card width + gaps
          maxWidth: isDesktop
            ? (baseCardWidth * 4 + gap * 3)
            : isTablet
            ? (baseCardWidth * 3 + gap * 2)
            : '100%', // On mobile, let it be 100% and cards will flex within
          // Removed padding here to let the outermost div handle it and give more room to flex items
        }}
      >
        {teamMembers.map((member, idx) => (
          <Card member={member} key={idx} />
        ))}
      </div>
    </div>
  );
};
 
export default TeamPage;