import { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Image } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import 'animate.css';

gsap.registerPlugin(ScrollTrigger);

const executiveSearchData = {
    hero: {
        title: "Executive Search Services",
        subtitle: "As one of the topmost executive search firms in India, with experience spanning over 2 decades, we help Fortune 500 companies find their top leaders and accelerate their productivity.",
        backgroundImage: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80'
    },
    sections: [
        {
            id: 'introduction',
            title: "Leading Executive Search Company in India",
            texts: [
                "At Makonis, we have a deep understanding of the client's business requirements, industry dynamics and functional know-how. Our access to a wider network of senior leaders and analytical capabilities enables us to provide you with the best of the best senior-level executives for your business needs.",
                "To help you understand and have your pick of higher-level executives, we've sub-divided our Executive Search hierarchy into three major categories: C-suite (CXO), MD, Chairperson, Board of Directors; Business Head, Country Manager, VP, AVP, Directors, GM and Functional Head; and Niche roles."
            ],
            image: 'https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            alt: 'Executive search consultancy meeting',
            reversed: false
        },
        {
            id: 'operations',
            title: "How Our Executive Search Team Operates",
            texts: [
                "For a standalone candidate search, the principal consultant is assigned the task of client engagement and profile recommendations, while the senior consultant takes up the responsibility of assessing potential candidate profiles. Our research associate handles mining and mapping the candidates as per their skill set.",
                "For multiple candidates search, the Principal Consultant stays the same even when there is a requirement for multiple candidates. Since there is a multiple-candidate search involved, several teams take up the responsibility of conducting diligent research based on role expertise."
            ],
            image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            alt: 'Executive search team collaboration',
            reversed: true
        },
        {
            id: 'benefits',
            title: "Benefits of Our Executive Search Services",
            texts: [
                // "As one of the best executive search firms in India, we have rightfully earned that place by providing top notch executive search services to clients regardless of the nature of their industry, their unique needs and size of the company."
            ],
            listItems: [
                "High Priority Recruitment: Since an executive search is conducted to hire for a senior level position, it is given the utmost priority with our most experienced search team assigned to it.",
                "Eliminates Risk of Bad Hires: Our best recruiters carry out the recruitment process eliminating the risk of bad hires through thorough vetting.",
                "Failproof Recruitment: Executive search is a reliable way to find a good leader for your organization who will keep your company culture intact.",
                "Access to Niche Skill Candidates: We offer access to a pool of talented candidates, both active and passive, especially passive candidates who are performing well in their current roles.",
                // "**Customized & Targeted Recruitment:** Executive search enables a more customized effort using special techniques to find the ideal candidate.",
                // "**Thoroughly Vetted Candidates:** Candidates presented are thoroughly vetted with all background checks done and references verified.",
                // "**Updates Throughout Process:** We provide market intelligence and competitor information at different stages of the recruitment process.",
                // "**Multiple Options:** We provide multiple shortlisted options based on salary, experience and other criteria for informed decision making."
            ],
            image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            alt: 'Executive search benefits and process',
            reversed: false
        },
        {
            id: 'industries',
            title: "Industries We Serve",
            texts: [
                "As one of the top executive search companies in India, we have served a wide range of industries and have found them their top-level leaders."
            ],
            listItems: [
                "Manufacturing",
                "Healthcare & Pharma", 
                "Retail & E-commerce",
                "Aerospace",
                "IT and Finance",
                "Pharmaceuticals and more"
            ],
            image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            alt: 'Various industries served by executive search',
            reversed: true
        }
    ]
};

const ExecutiveSearchPage = () => {
    const heroRef = useRef(null);
    const sectionsRef = useRef([]);

    useEffect(() => {
        window.scrollTo(0, 0);

        // GSAP Animations
        const ctx = gsap.context(() => {
            // Hero section animation
            gsap.from(heroRef.current.querySelector('h1'), {
                y: 100,
                opacity: 0,
                duration: 1.2,
                ease: "power3.out"
            });

            gsap.from(heroRef.current.querySelector('p'), {
                y: 50,
                opacity: 0,
                duration: 1,
                ease: "power2.out",
                delay: 0.3
            });

            gsap.from(heroRef.current.querySelector('.btn'), {
                y: 30,
                opacity: 0,
                duration: 0.8,
                ease: "back.out(1.7)",
                delay: 0.6
            });

            // Sections animations
            sectionsRef.current.forEach((section) => {
                if (section) {
                    gsap.from(section.querySelector('h2'), {
                        y: 50,
                        opacity: 0,
                        duration: 1,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: section,
                            start: "top 80%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    });

                    gsap.from(section.querySelectorAll('p'), {
                        y: 30,
                        opacity: 0,
                        duration: 0.8,
                        stagger: 0.2,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: section,
                            start: "top 75%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    });

                    gsap.from(section.querySelector('img'), {
                        scale: 0.8,
                        opacity: 0,
                        duration: 1,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: section,
                            start: "top 70%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    });
                }
            });



        }, heroRef);

        return () => ctx.revert();
    }, []);

    const [hoveredImage, setHoveredImage] = useState(null);

    // Reusable Style Objects
    const primaryColor = '#007bff';
    const primaryDarkColor = '#0056b3';
    const primaryRgb = '0,123,255';

    const ctaButtonBaseStyle = {
        padding: '1.2rem 3rem',
        fontSize: '1.2rem',
        background: `linear-gradient(95deg, ${primaryColor}, ${primaryDarkColor})`,
        border: 'none',
        borderRadius: '50px',
        boxShadow: `0 8px 25px rgba(${primaryRgb}, 0.3)`,
        transition: 'all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)',
        transform: 'translateY(0)',
        color: '#fff',
        textDecoration: 'none',
        display: 'inline-block'
    };



    const featureImageContainerStyle = (isHovered) => ({
        borderRadius: '1.25rem',
        overflow: 'hidden',
        boxShadow: isHovered ? '0 1.25rem 3.5rem rgba(0,0,0,0.2)' : '0 0.75rem 2rem rgba(0,0,0,0.1)',
        transition: 'transform 0.4s ease-out, box-shadow 0.4s ease-out',
        transform: isHovered ? 'scale(1.04) translateY(-8px)' : 'scale(1) translateY(0)',
        backgroundColor: '#f0f2f5',
    });

    const featureImageStyle = {
        width: '100%',
        height: '100%',
        minHeight: '400px',
        objectFit: 'cover',
        transition: 'transform 0.6s ease',
    };

    return (
        <div className="executive-search-page-wrapper">
            {/* Hero Section */}
            <section
                ref={heroRef}
                className="executive-search-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${executiveSearchData.hero.backgroundImage})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    minHeight: '100vh',
                    padding: '8rem 0'
                }}
            >
                {/* Animated Background Elements */}
                <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
                    {/* Floating Business Icons */}
                    {[
                        { icon: 'fa-user-tie', top: '15%', left: '10%', delay: 0 },
                        { icon: 'fa-chart-line', top: '25%', right: '15%', delay: 1 },
                        { icon: 'fa-handshake', bottom: '20%', left: '8%', delay: 2 },
                        { icon: 'fa-briefcase', bottom: '30%', right: '12%', delay: 3 }
                    ].map((item, index) => (
                        <div
                            key={index}
                            className="position-absolute"
                            style={{
                                ...item,
                                width: '60px',
                                height: '60px',
                                background: 'rgba(0, 160, 233, 0.1)',
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backdropFilter: 'blur(10px)',
                                border: '1px solid rgba(0, 160, 233, 0.2)',
                                animation: `float 6s ease-in-out infinite`,
                                animationDelay: `${item.delay}s`
                            }}
                        >
                            <i
                                className={`fas ${item.icon}`}
                                style={{
                                    color: 'rgba(0, 160, 233, 0.8)',
                                    fontSize: '1.5rem'
                                }}
                            />
                        </div>
                    ))}

                    {/* Gradient Orbs */}
                    <div
                        className="position-absolute"
                        style={{
                            width: '300px',
                            height: '300px',
                            background: 'radial-gradient(circle, rgba(0, 160, 233, 0.15) 0%, transparent 70%)',
                            borderRadius: '50%',
                            top: '20%',
                            right: '10%',
                            filter: 'blur(40px)',
                            animation: 'pulse 4s ease-in-out infinite'
                        }}
                    />
                </div>
                <Container className="position-relative z-index-1">
                    <h1 className="display-2 fw-bolder mb-4 animate__animated animate__fadeInDown animate__slow" style={{ textShadow: '3px 3px 6px rgba(0,0,0,0.6)' }}>
                        {executiveSearchData.hero.title}
                    </h1>
                    <p className="lead mb-5 mx-auto animate__animated animate__fadeInUp animate__slow" style={{ maxWidth: '800px', textShadow: '1px 1px 3px rgba(0,0,0,0.4)', fontSize: '1.35rem' }}>
                        {executiveSearchData.hero.subtitle}
                    </p>
                    <Link
                        to="/contact"
                        className="btn btn-lg rounded-pill fw-bold animate__animated animate__zoomIn animate__delay-1s"
                        style={{
                            ...ctaButtonBaseStyle,
                            backgroundColor: 'rgba(255,255,255,0.18)',
                            borderColor: 'rgba(255,255,255,0.6)',
                            backdropFilter: 'blur(8px)',
                            boxShadow: '0 6px 20px rgba(0,0,0,0.25)',
                            padding: '1.2rem 3.5rem',
                            fontSize: '1.25rem'
                        }}
                    >
                        Connect With Our Executive Search Experts <i className="fas fa-arrow-right ms-2"></i>
                    </Link>
                </Container>
            </section>

            {/* Feature Sections */}
            <div className="py-5 py-md-6" style={{
                background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)',
                backdropFilter: 'blur(10px)'
            }}>
                <Container>
                    {executiveSearchData.sections.map((section, idx) => (
                        <section
                            key={section.id}
                            ref={el => sectionsRef.current[idx] = el}
                            className="mb-5 mb-md-6 py-3"
                        >
                            {/* Centered Heading Above Content */}
                            <Row className="mb-4 mb-md-5">
                                <Col xs={12}>
                                    <h2 className="display-5 fw-bold text-center" style={{
                                        color: '#ffffff',
                                        lineHeight: '1.2',
                                        marginBottom: '0'
                                    }}>
                                        {section.title}
                                    </h2>
                                </Col>
                            </Row>

                            {/* Content and Image Row */}
                            <Row className={`align-items-center g-4 g-lg-5 ${section.reversed ? 'flex-row-reverse' : ''}`}>
                                <Col lg={6} md={12} className={`${section.reversed ? 'ps-lg-4' : 'pe-lg-4'}`}>
                                    <div className="content-wrapper">
                                        {section.texts.map((text, textIdx) => (
                                            <p key={textIdx} className="mb-3 mb-md-4" style={{
                                                fontSize: '1.1rem',
                                                lineHeight: '1.7',
                                                color: 'rgba(255, 255, 255, 0.9)',
                                                textAlign: 'justify'
                                            }}>
                                                {text}
                                            </p>
                                        ))}
                                        {section.listItems && (
                                            <div className="mt-3 mt-md-4 mb-3 mb-md-4">
                                                {section.listItems.map((item, itemIdx) => (
                                                    <div
                                                        key={itemIdx}
                                                        className="d-flex align-items-start mb-3"
                                                        style={{
                                                            fontSize: '1rem',
                                                            lineHeight: '1.6',
                                                            color: 'rgba(255, 255, 255, 0.85)'
                                                        }}
                                                    >
                                                        <i className="fas fa-check-circle me-3 flex-shrink-0 mt-1" style={{
                                                            fontSize: '1.1rem',
                                                            color: '#00a0e9'
                                                        }}></i>
                                                        <span style={{ textAlign: 'justify' }}>{item}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                        <Link
                                            to={`/services/executive-search/${section.id}`}
                                            className="btn btn-outline-light btn-sm mt-2 fw-semibold"
                                            style={{
                                                fontSize: '0.95rem',
                                                borderRadius: '25px',
                                                padding: '8px 20px',
                                                transition: 'all 0.3s ease',
                                                borderColor: '#00a0e9',
                                                color: '#00a0e9'
                                            }}
                                            onMouseEnter={(e) => {
                                                e.target.style.backgroundColor = '#00a0e9';
                                                e.target.style.color = '#ffffff';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.target.style.backgroundColor = 'transparent';
                                                e.target.style.color = '#00a0e9';
                                            }}
                                        >
                                            Learn More <i className="fas fa-arrow-right ms-2 small"></i>
                                        </Link>
                                    </div>
                                </Col>
                                <Col lg={6} md={12} className="d-flex justify-content-center">
                                    <div
                                        className="image-container"
                                        style={{
                                            ...featureImageContainerStyle(hoveredImage === section.id),
                                            maxWidth: '100%',
                                            width: '100%'
                                        }}
                                        onMouseEnter={() => setHoveredImage(section.id)}
                                        onMouseLeave={() => setHoveredImage(null)}
                                    >
                                        <Image
                                            src={section.image}
                                            alt={section.alt}
                                            fluid
                                            className="animate__animated animate__fadeInRight animate__delay-0.5s"
                                            style={{
                                                ...featureImageStyle,
                                                transform: hoveredImage === section.id ? 'scale(1.05)' : 'scale(1)',
                                                height: '350px',
                                                objectFit: 'cover'
                                            }}
                                        />
                                    </div>
                                </Col>
                            </Row>
                        </section>
                    ))}
                </Container>
            </div>


            {/* Global CSS */}
            <style>{`
                :root {
                    --bs-primary: ${primaryColor};
                    --bs-primary-dark: ${primaryDarkColor};
                    --bs-primary-rgb: ${primaryRgb};
                }

                h1, h2, h3, h4, h5, h6 {
                    line-height: 1.2;
                }

                p {
                    line-height: 1.75;
                }

                .container {
                    padding-left: 1.5rem;
                    padding-right: 1.5rem;
                }

                .py-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                .py-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                .py-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }

                .content-wrapper {
                    padding: 0;
                }

                .image-container {
                    position: relative;
                    overflow: hidden;
                }

                @keyframes float {
                    0%, 100% {
                        transform: translateY(0px) rotate(0deg);
                        opacity: 0.6;
                    }
                    33% {
                        transform: translateY(-15px) rotate(120deg);
                        opacity: 1;
                    }
                    66% {
                        transform: translateY(5px) rotate(240deg);
                        opacity: 0.8;
                    }
                }

                @keyframes pulse {
                    0%, 100% {
                        transform: scale(1);
                        opacity: 0.15;
                    }
                    50% {
                        transform: scale(1.1);
                        opacity: 0.25;
                    }
                }

                @media (min-width: 768px) {
                    .py-md-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                    .py-md-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                    .py-md-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }
                    .mb-md-5 { margin-bottom: 4rem !important; }
                    .mb-md-6 { margin-bottom: 6rem !important; }
                    .mb-md-8 { margin-bottom: 8rem !important; }
                }
            `}</style>
        </div>
    );
};

export default ExecutiveSearchPage;
