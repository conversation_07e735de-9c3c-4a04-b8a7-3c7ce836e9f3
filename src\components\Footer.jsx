import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {

  return (
    <footer className="text-white py-12 lg:py-16 text-sm bg-makonis-primary/95 backdrop-blur-sm">
      <div className="container-makonis">
        {/* Top Section with Logo, Description, Links, and Contact */}
        <div className="container-makonis max-w-xl mx-auto px-4 text-center mb-12">
    <h2 className="text-2xl font-bold mb-4">Stay Updated</h2>
    
    <form className="flex items-center rounded overflow-hidden bg-[#04406F]">
      <input
        type="email"
        placeholder="Enter your email"
        className="flex-grow px-4 py-2 bg-transparent text-white placeholder-white/60 focus:outline-none"
      />
      <button
        type="submit"
        className="bg-[#00AEEF] hover:bg-[#0095d9] px-4 py-2 transition duration-300"
      >
        <i className="fas fa-paper-plane text-white"></i>
      </button>
    </form>
  </div>
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-12 pb-8 border-b border-white/20">
          <div className="lg:col-span-4 xl:col-span-3 pr-0 lg:pr-8">
            <div className="flex items-center mb-4">
              {/* Enhanced Logo */}
              <div className="w-12 h-12 bg-makonis-gradient rounded-xl flex items-center justify-center mr-3 shadow-glow">
                <i className="fas fa-code text-white text-lg"></i>
              </div>
              <Link to="/" className="text-decoration-none">
                 <h2 className="text-xl font-bold text-white mb-0">Makonis Software</h2>
              </Link>
            </div>
            <p className="text-white/75 mb-6 leading-relaxed">
              Innovative software solutions to transform your digital presence and achieve your business goals.
            </p>
             {/* Enhanced Newsletter Form */}
            {/* <form className="mb-4">
              <label className="block mb-2 text-white font-medium">Stay Updated</label>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 bg-white/10 text-white placeholder-white/60 border-0 rounded-l-lg focus:outline-none focus:bg-white/15 focus:ring-2 focus:ring-makonis-secondary/50 transition-all duration-300"
                />
                <button
                  type="submit"
                  className="px-4 py-3 bg-makonis-secondary hover:bg-makonis-secondary/90 text-white rounded-r-lg transition-all duration-300 hover:scale-105"
                >
                  <i className="fas fa-paper-plane"></i>
                </button>
              </div>
            </form> */}
          </div>

          <div className="lg:col-span-8 xl:col-span-9">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
              <div>
                <h5 className="text-white font-semibold mb-4 tracking-wide">About Us</h5>
                <ul className="space-y-2">
                  {[
                    { label: 'Company', href: '/#' },
                    { label: 'Our Team', href: '/#' },
                    { label: 'Careers', href: '/#' },
                    { label: 'Blog', href: '/#' }
                  ].map(link => (
                    <li key={link.label}>
                      <Link to={link.href} className="footer-link-enhanced group">
                        <i className="fas fa-chevron-right text-xs text-makonis-white mr-2 transition-transform duration-200 group-hover:translate-x-1"></i>
                        <span>{link.label}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h5 className="text-white font-semibold mb-4 tracking-wide">Services</h5>
                <ul className="space-y-2">
                  {[
                    { label: 'Artificial Intelligence', to: '/ai' },
                    { label: 'Data Analytics', to: '/analytics' },
                    { label: 'IoT Solutions', to: '/iot' },
                    { label: 'Web & Mobile Dev', to: '/webdev' },
                    { label: 'Testing Services', to: '/testing' },
                    { label: 'Embedded Systems', to: '/embedded' },
                  ].map(service => (
                     <li key={service.label}>
                      <Link to={service.to} className="footer-link-enhanced group">
                         <i className="fas fa-chevron-right text-xs text-makonis-white mr-2 transition-transform duration-200 group-hover:translate-x-1"></i>
                         <span>{service.label}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h5 className="text-white font-semibold mb-4 tracking-wide">Contact Us</h5>
                <ul className="space-y-3">
                  <li className="flex">
                    <i className="fas fa-map-marker-alt text-makonis-white mt-1 mr-3 flex-shrink-0 w-4"></i>
                    <span className="text-white/75">51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037</span>
                  </li>
                  <li className="flex">
                    <i className="fas fa-envelope text-makonis-white mt-1 mr-3 flex-shrink-0 w-4"></i>
                    <a href="mailto:<EMAIL>" className="footer-link-enhanced"><EMAIL></a>
                  </li>
                  <li className="flex">
                    <i className="fas fa-phone-alt text-makonis-white mt-1 mr-3 flex-shrink-0 w-4"></i>
                    <a href="tel:+918041707838" className="footer-link-enhanced">+91 8041707838</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Bottom Section with Copyright and Social Icons */}
        <div className="flex flex-col md:flex-row items-center justify-between pt-0">
          <div className="text-center md:text-left mb-4 md:mb-0">
            <p className="text-white/75 text-sm">
              &copy; {new Date().getFullYear()} Makonis Software Solutions. All Rights Reserved.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {[
              { label: 'Facebook', href: '#', icon: 'fab fa-facebook-f' },
              { label: 'Twitter', href: '#', icon: 'fab fa-twitter' },
              { label: 'LinkedIn', href: '#', icon: 'fab fa-linkedin-in' },
              { label: 'Instagram', href: '#', icon: 'fab fa-instagram' }
            ].map(social => (
              <a
                href={social.href}
                className="social-icon-enhanced group"
                key={social.label}
                aria-label={social.label}
                target="_blank"
                rel="noopener noreferrer"
              >
                <div className="w-10 h-10 rounded-full bg-white/10 border border-white/15 flex items-center justify-center transition-all duration-300 group-hover:bg-makonis-secondary group-hover:border-makonis-secondary group-hover:-translate-y-1 group-hover:scale-105 group-hover:shadow-glow">
                  <i className={`${social.icon} text-white transition-colors duration-300`}></i>
                </div>
              </a>
            ))}
          </div>
        </div>
      </div>


    </footer>
  );
};

export default Footer;