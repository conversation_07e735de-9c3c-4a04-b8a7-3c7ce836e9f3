import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

// Enhanced SVG Blob for a more modern feel
const ModernBlob1 = () => (
  <svg width="500" height="500" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.08, position: 'absolute', top: '-100px', right: '-150px' }}>
    <defs>
      <linearGradient id="blobGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#007bff', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path fill="url(#blobGradient1)" d="M60.1,-66.9C76.5,-56.8,87.5,-37.9,89.9,-18.3C92.3,1.3,86.1,21.5,74.1,37.9C62.1,54.3,44.3,67,25.5,73.7C6.7,80.4,-13.2,81.2,-30.9,74.8C-48.6,68.4,-64.1,54.8,-72.4,38.2C-80.7,21.6,-81.8,2,-76.5,-16.1C-71.2,-34.2,-59.5,-50.8,-44.4,-61C-29.3,-71.1,-10.8,-74.7,9.3,-77.2C29.4,-79.7,51.1,-82.3,60.1,-66.9Z" transform="translate(100 100) scale(1.1)" />
  </svg>
);

const ModernBlob2 = () => (
  <svg width="400" height="400" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.07, position: 'absolute', bottom: '-120px', left: '-100px' }}>
     <defs>
      <linearGradient id="blobGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#6f42c1', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path fill="url(#blobGradient2)" d="M50.9,-57.9C65.9,-47.7,78.1,-33.5,81.2,-17.2C84.3,-0.9,78.3,17.6,67.3,32.8C56.3,48,40.3,59.9,23.3,66.6C6.2,73.3,-11.9,74.8,-28.7,69.8C-45.5,64.8,-61,53.3,-69.5,38.3C-77.9,23.3,-79.3,4.8,-74.7,-12.6C-70.1,-30,-59.5,-46.3,-45.7,-56.6C-31.9,-66.9,-14.9,-71.2,2.4,-73.2C19.7,-75.2,35.9,-68.1,50.9,-57.9Z" transform="translate(100 100) scale(0.9)" />
  </svg>
);

// No local imports needed - using web images

const GallerySection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState({});
  const dotsRef = useRef([]);

  // GSAP animation refs
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const carouselRef = useRef(null);
  const backgroundRef = useRef(null);

  const galleryImages = [
    {
      id: 1,
      src: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1484&q=80',
      alt: 'Collaborative Workspace',
      caption: 'Team Collaboration Hub',
    },
    {
      id: 2,
      src: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Product Strategy Meeting',
      caption: 'Strategic Planning Session',
    },
    {
      id: 3,
      src: 'https://images.unsplash.com/photo-1517048676732-d65bc937f952?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Company Town Hall',
      caption: 'All-Hands Meeting',
    },
    {
      id: 4,
      src: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Brainstorming Session',
      caption: 'Innovation Workshop',
    },
    {
      id: 5,
      src: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Modern Office Interior',
      caption: 'Our Inspiring Workplace',
    },
    {
      id: 6,
      src: 'https://images.unsplash.com/photo-1581093450021-4a7360e9a6b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Technology Lab',
      caption: 'R&D and Innovation Lab',
    },
     {
      id: 7,
      src: 'https://images.unsplash.com/photo-1573496774439-fe217036005c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Team Recognition Event',
      caption: 'Celebrating Success',
    },
    {
      id: 8,
      src: 'https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
      alt: 'Client Presentation',
      caption: 'Client Partnership',
    },
     {
      id: 9,
      src: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1471&q=80',
      alt: 'Team lunch event',
      caption: 'Company Culture',
    }
  ];

  // Group images into slides of 3
  const imagesPerSlide = 3;
  const slides = [];
  for (let i = 0; i < galleryImages.length; i += imagesPerSlide) {
    slides.push(galleryImages.slice(i, i + imagesPerSlide));
  }
  const totalSlides = slides.length;

  // Auto-advance carousel functionality
  useEffect(() => {
    if (!isPlaying || isHovered) return;

    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % totalSlides);
    }, 4000); // 4 seconds per slide

    return () => clearInterval(interval);
  }, [isPlaying, isHovered, totalSlides]);

  // Handle image loading
  const handleImageLoad = (imageId) => {
    setImageLoaded(prev => ({ ...prev, [imageId]: true }));
  };

  // Navigation functions
  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // GSAP animations for dot indicators
  useEffect(() => {
    dotsRef.current.forEach((dot, index) => {
      if (dot) {
        gsap.to(dot, {
          scale: index === currentSlide ? 1.2 : 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  }, [currentSlide]);

  // GSAP scroll-triggered animations (from ServicesSection)
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.from(titleRef.current, {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Carousel stagger animation
      gsap.from(carouselRef.current, {
        y: 80,
        opacity: 0,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 70%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Background parallax effect
      gsap.to(backgroundRef.current, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="section-padding relative overflow-hidden"
      id="gallery-section"
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)',
        backdropFilter: 'blur(10px)',
        display: 'flex',
        alignItems: 'center',
        padding: '80px 0'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Enhanced Background Elements */}
      <div ref={backgroundRef} className="absolute inset-0 z-0">
        {/* Animated Grid Pattern */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
          }}
        />

        {/* Floating Tech Icons */}
        {[
          { lordicon: 'https://cdn.lordicon.com/qhviklyi.json', top: '10%', left: '5%', delay: 0 },
          { lordicon: 'https://cdn.lordicon.com/kiynvdns.json', top: '20%', right: '8%', delay: 1 },
          { lordicon: 'https://cdn.lordicon.com/hwjcdycb.json', bottom: '15%', left: '3%', delay: 2 },
          { lordicon: 'https://cdn.lordicon.com/qhgmphtg.json', bottom: '25%', right: '5%', delay: 3 }
        ].map((item, index) => (
          <div
            key={index}
            className="absolute w-15 h-15 bg-makonis-secondary/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-makonis-secondary/20 animate-float"
            style={{
              ...item,
              animationDelay: `${item.delay}s`
            }}
          >
            <lord-icon
              src={item.lordicon}
              trigger="hover"
              colors="primary:#00a0e9"
              style={{ width: '24px', height: '24px' }}>
            </lord-icon>
          </div>
        ))}

        <ModernBlob1 />
        <ModernBlob2 />
      </div>

      <div className="container-makonis relative z-10">
        <div ref={titleRef} className="text-center mb-16 lg:mb-24">
         

          <h2  
          style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}>
            Inside Our Office Walls
          </h2>

          <p className="text-xl text-white/90 leading-relaxed mx-auto mb-4 max-w-3xl">
            A glimpse into our vibrant culture, collaborative spaces, and memorable moments.
          </p>

          {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>
        </div>

        {/* Carousel Container */}
        <div ref={carouselRef} className="position-relative" style={{ height: '500px', overflow: 'hidden' }}>
          {/* Carousel Track */}
          <div
            className="d-flex h-100"
            style={{
              transform: `translateX(-${currentSlide * 100}%)`,
              transition: 'transform 1s cubic-bezier(0.76, 0, 0.24, 1)',
            }}
          >
            {/* Map over the grouped slides */}
            {slides.map((slide, slideIndex) => (
              <div
                key={slideIndex}
                className="w-100 h-100 flex-shrink-0 d-flex justify-content-center align-items-center"
                style={{ padding: '0 100px' }} // Provides space for nav buttons
              >
                {/* Inner container for the 3 images */}
                <div className="d-flex w-100 h-100" style={{ gap: '20px' }}>
                  {slide.map((image) => (
                    <div
                      key={image.id}
                      className="position-relative h-100"
                      style={{
                        flex: 1, // Each image container takes equal space
                        borderRadius: '16px',
                        overflow: 'hidden',
                        transition: 'transform 0.4s ease, box-shadow 0.4s ease',
                        boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-10px) scale(1.03)';
                        e.currentTarget.style.boxShadow = '0 20px 40px rgba(0, 160, 233, 0.4)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0) scale(1)';
                        e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
                      }}
                    >
                      <img
                        src={image.src}
                        alt={image.alt}
                        className="w-100 h-100"
                        style={{ objectFit: 'cover' }}
                        onLoad={() => handleImageLoad(image.id)}
                      />
                      {/* Caption Overlay */}
                      <div className="position-absolute bottom-0 start-0 w-100 p-3"
                        style={{
                          background: 'linear-gradient(to top, rgba(0, 25, 55, 0.9) 0%, transparent 100%)',
                          color: 'white'
                        }}
                      >
                        <h6 className="fw-bold mb-0">{image.caption}</h6>
                      </div>
                      
                      {/* Loading State */}
                      {!imageLoaded[image.id] && (
                        <div className="position-absolute w-100 h-100 d-flex align-items-center justify-content-center" style={{ background: 'rgba(0, 41, 86, 0.9)', top: 0, left: 0, zIndex: 4 }}>
                          <div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Controls */}
          <div className="position-absolute top-50 start-0 translate-middle-y" style={{ zIndex: 6 }}>
            <button onClick={goToPrevious} className="btn nav-btn" aria-label="Previous images"><i className="fas fa-chevron-left"></i></button>
          </div>
          <div className="position-absolute top-50 end-0 translate-middle-y" style={{ zIndex: 6 }}>
            <button onClick={goToNext} className="btn nav-btn" aria-label="Next images"><i className="fas fa-chevron-right"></i></button>
          </div>
          <div className="position-absolute top-0 end-0 m-3" style={{ zIndex: 6 }}>
            <button onClick={togglePlayPause} className="btn nav-btn-play" aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}>
              <i className={`fas fa-${isPlaying ? 'pause' : 'play'}`}></i>
            </button>
          </div>
        </div>

        {/* Dot Indicators */}
        {/* <div className="d-flex justify-content-center align-items-center mt-5">
          {slides.map((_, index) => (
            <button
              key={index}
              ref={el => dotsRef.current[index] = el}
              onClick={() => goToSlide(index)}
              className="btn p-0 mx-2 dot-indicator"
              style={{
                width: index === currentSlide ? '16px' : '12px',
                height: index === currentSlide ? '16px' : '12px',
                background: index === currentSlide ? 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)' : 'rgba(255, 255, 255, 0.4)',
                border: index === currentSlide ? '2px solid rgba(255, 255, 255, 0.5)' : '1px solid rgba(255, 255, 255, 0.3)',
                boxShadow: index === currentSlide ? '0 0 25px rgba(0, 160, 233, 0.6)' : '0 2px 8px rgba(0, 0, 0, 0.2)',
              }}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div> */}
      </div>

      {/* CSS Animations and Styles */}
      <style>{`
        @keyframes float { 0%, 100% { transform: translateY(0px) rotate(0deg); } 33% { transform: translateY(-15px) rotate(120deg); } 66% { transform: translateY(5px) rotate(240deg); } }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
        
        .nav-btn, .nav-btn-play {
          width: 50px;
          height: 50px;
          background: linear-gradient(135deg, rgba(0, 160, 233, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
          backdrop-filter: blur(15px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          color: white;
          font-size: 16px;
          transition: all 0.4s ease;
          box-shadow: 0 8px 25px rgba(0, 43, 89, 0.2);
        }
        .nav-btn:hover {
          background: linear-gradient(135deg, rgba(0, 160, 233, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%);
          transform: scale(1.1);
          box-shadow: 0 10px 30px rgba(0, 160, 233, 0.3);
        }
        .nav-btn-play {
          animation: ${!isPlaying ? 'pulse 2s ease-in-out infinite' : 'none'};
        }
        .dot-indicator {
          border-radius: 50%;
          transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
      `}</style>
    </section>
  );
};

export default GallerySection;